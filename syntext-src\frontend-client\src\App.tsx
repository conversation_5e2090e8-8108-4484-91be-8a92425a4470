import React, { useState, useEffect } from 'react';
import { User, Settings, Headphones, MessageCircle, Bell, LogOut } from 'lucide-react';
import TranslationInterface from './components/TranslationInterface';
import UserDashboard from './components/UserDashboard';
import AuthForm from './components/AuthForm';

function App() {
  console.log('App component rendering...');
  const [currentView, setCurrentView] = useState<'translation' | 'user'>('translation');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  console.log('App state:', { currentView, isAuthenticated, currentUser });

  useEffect(() => {
    // 检查本地存储的认证信息
    const token = localStorage.getItem('user_token');
    const user = localStorage.getItem('user_data');

    if (token && user) {
      try {
        const userData = JSON.parse(user);
        setCurrentUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        // 清除无效的认证信息
        localStorage.removeItem('user_token');
        localStorage.removeItem('user_data');
      }
    }
  }, []);

  const handleLogin = (user: any) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    localStorage.removeItem('user_token');
    localStorage.removeItem('user_data');
    setCurrentUser(null);
    setIsAuthenticated(false);
  };

  if (!isAuthenticated) {
    return <AuthForm onLogin={handleLogin} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Navigation Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Headphones className="w-8 h-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">SynText</span>
              </div>
            </div>
            
            <nav className="flex items-center space-x-8">
              <button
                onClick={() => setCurrentView('translation')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                  currentView === 'translation' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <MessageCircle className="w-4 h-4" />
                <span>实时翻译</span>
              </button>
              
              <button
                onClick={() => setCurrentView('user')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                  currentView === 'user' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                <User className="w-4 h-4" />
                <span>用户中心</span>
              </button>
            </nav>

            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                <Bell className="w-5 h-5" />
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-700">
                  {currentUser?.nickname || currentUser?.email}
                </span>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="退出登录"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'translation' && <TranslationInterface />}
        {currentView === 'user' && <UserDashboard />}
      </main>
    </div>
  );
}

export default App;
