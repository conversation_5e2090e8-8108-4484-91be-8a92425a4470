import React, { useState } from 'react';
import AuthForm from './components/AuthForm';

function SimpleAuthApp() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const handleLogin = (user: any) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
  };

  if (!isAuthenticated) {
    return <AuthForm onLogin={handleLogin} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">登录成功！</h1>
        <p className="text-gray-600 mb-4">欢迎，{currentUser?.nickname || currentUser?.email}</p>
        <button
          onClick={() => {
            localStorage.removeItem('user_token');
            localStorage.removeItem('user_data');
            setIsAuthenticated(false);
            setCurrentUser(null);
          }}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          退出登录
        </button>
      </div>
    </div>
  );
}

export default SimpleAuthApp;
