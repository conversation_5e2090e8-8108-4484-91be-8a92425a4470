import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full">
            <h1 className="text-2xl font-bold text-red-600 mb-4">应用程序错误</h1>
            <p className="text-gray-600 mb-4">应用程序遇到了一个错误，无法正常显示。</p>
            
            {this.state.error && (
              <div className="bg-red-100 border border-red-300 rounded p-4 mb-4">
                <h3 className="font-semibold text-red-800 mb-2">错误信息：</h3>
                <p className="text-red-700 font-mono text-sm">{this.state.error.message}</p>
              </div>
            )}
            
            {this.state.errorInfo && (
              <div className="bg-gray-100 border border-gray-300 rounded p-4 mb-4">
                <h3 className="font-semibold text-gray-800 mb-2">错误堆栈：</h3>
                <pre className="text-gray-700 text-xs overflow-auto max-h-40">
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}
            
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              重新加载页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
