import React, { useState } from 'react';
import { User, Headphones } from 'lucide-react';

function SimpleApp() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl flex items-center justify-center shadow-lg">
                <Headphones className="w-7 h-7 text-white" />
              </div>
              <div className="text-left">
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-700 bg-clip-text text-transparent">SynText</span>
                <p className="text-sm text-gray-600 font-medium">实时翻译系统</p>
              </div>
            </div>
            <h1 className="text-xl font-semibold text-gray-800 mb-2">欢迎回来</h1>
            <p className="text-sm text-gray-500">继续您的翻译体验</p>
          </div>

          <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-8">
            <button
              onClick={() => setIsAuthenticated(true)}
              className="w-full py-3 rounded-lg font-medium bg-gradient-to-r from-blue-600 to-purple-700 text-white hover:from-blue-700 hover:to-purple-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
            >
              模拟登录
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Headphones className="w-8 h-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">SynText</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-700">测试用户</span>
              </div>
              <button
                onClick={() => setIsAuthenticated(false)}
                className="px-4 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">简化版应用</h2>
          <p className="text-gray-600">这是一个简化版本的应用，用于测试基本功能。</p>
          <div className="mt-4 p-4 bg-green-100 border border-green-300 rounded">
            <p className="text-green-800">✅ React 组件渲染正常</p>
            <p className="text-green-800">✅ Tailwind CSS 样式正常</p>
            <p className="text-green-800">✅ Lucide React 图标正常</p>
            <p className="text-green-800">✅ 状态管理正常</p>
          </div>
        </div>
      </main>
    </div>
  );
}

export default SimpleApp;
