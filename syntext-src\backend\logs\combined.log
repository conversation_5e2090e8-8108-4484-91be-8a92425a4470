{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T09:35:06.710Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T09:35:06.712Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T09:35:06.713Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T09:35:06.714Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T09:35:06.714Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T09:35:06.715Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T09:35:06.716Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T09:35:06.717Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T09:35:06.717Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T09:35:06.718Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T09:35:06.719Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T09:35:06.720Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T09:35:06.721Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T09:35:06.721Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T09:35:06.723Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:43:34.090Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:49:05.075Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-04T11:55:22.819Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-04T11:55:22.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-04T11:55:22.821Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-04T11:55:22.822Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/profile, GET} route","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RoutesResolver","level":"info","message":"UserController {/api/v1/users}:","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, POST} route","timestamp":"2025-08-04T11:55:22.823Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users, GET} route","timestamp":"2025-08-04T11:55:22.824Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/stats, GET} route","timestamp":"2025-08-04T11:55:22.824Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, GET} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, PATCH} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/users/:id, DELETE} route","timestamp":"2025-08-04T11:55:22.825Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translations}:","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/history, GET} route","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translations/stats, GET} route","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscriptions}:","timestamp":"2025-08-04T11:55:22.826Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/plans, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/current, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscriptions/history, GET} route","timestamp":"2025-08-04T11:55:22.827Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupons}:","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons, GET} route","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupons/validate, POST} route","timestamp":"2025-08-04T11:55:22.828Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-04T11:55:22.830Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T11:57:03.501Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 589384","timestamp":"2025-08-04T11:57:03.502Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:04:04.967Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 631628","timestamp":"2025-08-04T12:04:04.969Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:05:07.268Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 470810","timestamp":"2025-08-04T12:05:07.268Z"}
{"context":"EmailService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-04T12:11:58.215Z"}
{"context":"EmailService","level":"info","message":"[DEV] Verification <NAME_EMAIL>: 303976","timestamp":"2025-08-04T12:11:58.215Z"}
