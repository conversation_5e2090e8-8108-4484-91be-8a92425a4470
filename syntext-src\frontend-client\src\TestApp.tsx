import React from 'react';

function TestApp() {
  return (
    <div className="min-h-screen bg-blue-100 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">测试页面</h1>
        <p className="text-gray-600">如果您能看到这个页面，说明 React 和 Tailwind CSS 都正常工作。</p>
        <div className="mt-4 p-4 bg-green-100 border border-green-300 rounded">
          <p className="text-green-800">✅ React 渲染正常</p>
          <p className="text-green-800">✅ Tailwind CSS 样式正常</p>
        </div>
      </div>
    </div>
  );
}

export default TestApp;
